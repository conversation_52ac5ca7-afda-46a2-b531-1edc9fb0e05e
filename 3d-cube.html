<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D立体魔方</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            perspective: 1000px;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            margin-bottom: 20px;
        }

        .cube-container {
            position: relative;
            width: 200px;
            height: 200px;
            transform-style: preserve-3d;
            animation: rotate 10s infinite linear;
        }

        .cube-container:hover {
            animation-play-state: paused;
        }

        .cube {
            position: absolute;
            width: 200px;
            height: 200px;
            transform-style: preserve-3d;
        }

        .face {
            position: absolute;
            width: 200px;
            height: 200px;
            border: 3px solid #333;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 2px;
            padding: 5px;
        }

        .square {
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease;
        }

        .square:hover {
            transform: scale(1.1);
            box-shadow: inset 0 0 15px rgba(255, 255, 255, 0.5);
        }

        /* 魔方六个面的颜色 */
        .front {
            background: rgba(255, 255, 255, 0.9);
            transform: rotateY(0deg) translateZ(100px);
        }

        .back {
            background: rgba(255, 255, 0, 0.9);
            transform: rotateY(180deg) translateZ(100px);
        }

        .right {
            background: rgba(255, 165, 0, 0.9);
            transform: rotateY(90deg) translateZ(100px);
        }

        .left {
            background: rgba(255, 0, 0, 0.9);
            transform: rotateY(-90deg) translateZ(100px);
        }

        .top {
            background: rgba(0, 128, 0, 0.9);
            transform: rotateX(90deg) translateZ(100px);
        }

        .bottom {
            background: rgba(0, 0, 255, 0.9);
            transform: rotateX(-90deg) translateZ(100px);
        }

        /* 每个面的小方块颜色 */
        .front .square { background: #ffffff; }
        .back .square { background: #ffff00; }
        .right .square { background: #ffa500; }
        .left .square { background: #ff0000; }
        .top .square { background: #008000; }
        .bottom .square { background: #0000ff; }

        @keyframes rotate {
            0% { transform: rotateX(0deg) rotateY(0deg); }
            25% { transform: rotateX(90deg) rotateY(90deg); }
            50% { transform: rotateX(180deg) rotateY(180deg); }
            75% { transform: rotateX(270deg) rotateY(270deg); }
            100% { transform: rotateX(360deg) rotateY(360deg); }
        }

        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .info {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 16px;
            max-width: 400px;
            line-height: 1.5;
        }

        /* 添加一些装饰性的背景元素 */
        .bg-decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .floating-cube {
            position: absolute;
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="bg-decoration">
        <div class="floating-cube" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
        <div class="floating-cube" style="top: 20%; right: 15%; animation-delay: 1s;"></div>
        <div class="floating-cube" style="bottom: 30%; left: 20%; animation-delay: 2s;"></div>
        <div class="floating-cube" style="bottom: 10%; right: 10%; animation-delay: 3s;"></div>
    </div>

    <div class="container">
        <h1 class="title">3D立体魔方</h1>
        
        <div class="cube-container" id="cubeContainer">
            <div class="cube">
                <!-- 前面 (白色) -->
                <div class="face front">
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                </div>
                
                <!-- 后面 (黄色) -->
                <div class="face back">
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                </div>
                
                <!-- 右面 (橙色) -->
                <div class="face right">
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                </div>
                
                <!-- 左面 (红色) -->
                <div class="face left">
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                </div>
                
                <!-- 上面 (绿色) -->
                <div class="face top">
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                </div>
                
                <!-- 下面 (蓝色) -->
                <div class="face bottom">
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                    <div class="square"></div><div class="square"></div><div class="square"></div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="rotateCube('x', 90)">向上翻转</button>
            <button class="control-btn" onclick="rotateCube('x', -90)">向下翻转</button>
            <button class="control-btn" onclick="rotateCube('y', 90)">向右旋转</button>
            <button class="control-btn" onclick="rotateCube('y', -90)">向左旋转</button>
            <button class="control-btn" onclick="resetCube()">重置</button>
            <button class="control-btn" onclick="toggleAnimation()">暂停/继续</button>
        </div>

        <div class="info">
            鼠标悬停在魔方上可暂停自动旋转<br>
            点击控制按钮可手动旋转魔方<br>
            每个小方块都可以悬停交互
        </div>
    </div>

    <script>
        let currentRotationX = 0;
        let currentRotationY = 0;
        let isAnimationPaused = false;
        const cubeContainer = document.getElementById('cubeContainer');

        function rotateCube(axis, angle) {
            if (axis === 'x') {
                currentRotationX += angle;
            } else if (axis === 'y') {
                currentRotationY += angle;
            }

            cubeContainer.style.transform = `rotateX(${currentRotationX}deg) rotateY(${currentRotationY}deg)`;
            cubeContainer.style.animation = 'none';

            setTimeout(() => {
                if (!isAnimationPaused) {
                    cubeContainer.style.animation = 'rotate 10s infinite linear';
                }
            }, 500);
        }

        function resetCube() {
            currentRotationX = 0;
            currentRotationY = 0;
            cubeContainer.style.transform = 'rotateX(0deg) rotateY(0deg)';
            cubeContainer.style.animation = 'rotate 10s infinite linear';
            isAnimationPaused = false;
        }

        function toggleAnimation() {
            if (isAnimationPaused) {
                cubeContainer.style.animationPlayState = 'running';
                isAnimationPaused = false;
            } else {
                cubeContainer.style.animationPlayState = 'paused';
                isAnimationPaused = true;
            }
        }

        // 添加键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowUp':
                    rotateCube('x', -90);
                    break;
                case 'ArrowDown':
                    rotateCube('x', 90);
                    break;
                case 'ArrowLeft':
                    rotateCube('y', -90);
                    break;
                case 'ArrowRight':
                    rotateCube('y', 90);
                    break;
                case ' ':
                    e.preventDefault();
                    toggleAnimation();
                    break;
                case 'r':
                case 'R':
                    resetCube();
                    break;
            }
        });

        // 随机改变小方块颜色的函数
        function randomizeColors() {
            const squares = document.querySelectorAll('.square');
            const colors = ['#ffffff', '#ffff00', '#ffa500', '#ff0000', '#008000', '#0000ff'];

            squares.forEach(square => {
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                square.style.backgroundColor = randomColor;
            });
        }

        // 双击魔方随机化颜色
        cubeContainer.addEventListener('dblclick', randomizeColors);

        // 添加触摸支持
        let startX, startY;
        cubeContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        cubeContainer.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const deltaX = e.touches[0].clientX - startX;
            const deltaY = e.touches[0].clientY - startY;

            currentRotationY += deltaX * 0.5;
            currentRotationX -= deltaY * 0.5;

            cubeContainer.style.transform = `rotateX(${currentRotationX}deg) rotateY(${currentRotationY}deg)`;
            cubeContainer.style.animation = 'none';

            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        cubeContainer.addEventListener('touchend', () => {
            setTimeout(() => {
                if (!isAnimationPaused) {
                    cubeContainer.style.animation = 'rotate 10s infinite linear';
                }
            }, 1000);
        });
    </script>
</body>
</html>
