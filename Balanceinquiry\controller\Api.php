<?php

namespace plugin\Balanceinquiry\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许用户访问
    
    protected $noNeedLogin = [];

    // 查询页面
    public function index() {
        return View::fetch();
    }

    // 获取所有用户余额信息
    public function getBalance() {
        try {
            $search = input('search', '');
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);

            // 构建查询
            $query = Db::name('user')
                ->field(['username', 'platform_money'])
                ->order('platform_money', 'desc');

            // 如果有搜索关键词，添加搜索条件
            if (!empty($search)) {
                $query->where('username', 'like', "%{$search}%");
            }

            // 获取总数
            $total = $query->count();

            // 执行分页查询
            $userList = $query->page($page, $limit)->select()->toArray();

            if (empty($userList)) {
                return json(['code' => 404, 'msg' => '没有用户数据']);
            }

            // 计算总余额
            $totalBalance = Db::name('user')->sum('platform_money');

            // 格式化数据
            $formattedList = array_map(function($user) {
                return [
                    'username' => $user['username'],
                    'balance' => number_format(floatval($user['platform_money']), 2)
                ];
            }, $userList);

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $formattedList,
                'total' => $total,
                'totalBalance' => number_format($totalBalance, 2)
            ]);

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('余额查询失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 清除用户余额
    public function clearBalance() {
        try {
            $username = input('username', '');

            if (empty($username)) {
                return json(['code' => 400, 'msg' => '用户名不能为空']);
            }

            // 查找用户
            $user = Db::name('user')->where('username', $username)->find();
            if (!$user) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }

            $currentBalance = floatval($user['platform_money']);

            // 如果余额已经为0，无需操作
            if ($currentBalance == 0) {
                return json(['code' => 200, 'msg' => '用户余额已为0，无需清除']);
            }

            // 开启事务
            Db::startTrans();

            try {
                // 更新用户余额为0
                Db::name('user')
                    ->where('username', $username)
                    ->update(['platform_money' => 0]);

                // 记录资金变动日志
                Db::name('user_money_log')->insert([
                    'user_id' => $user['id'],
                    'change' => -$currentBalance,
                    'reason' => '管理员清除余额',
                    'source' => 'Platform',
                    'create_time' => time()
                ]);

                // 提交事务
                Db::commit();

                // 记录操作日志
                \think\facade\Log::info("管理员清除用户余额：用户={$username}, 清除金额={$currentBalance}");

                return json([
                    'code' => 200,
                    'msg' => '余额清除成功',
                    'data' => [
                        'username' => $username,
                        'cleared_amount' => number_format($currentBalance, 2)
                    ]
                ]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('清除余额失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '清除失败：' . $e->getMessage()]);
        }
    }

    // 批量清除所有用户余额
    public function clearAllBalance() {
        try {
            $confirmCode = input('confirm_code', '');

            // 安全确认码验证
            if ($confirmCode !== 'CLEAR_ALL_BALANCE_CONFIRM') {
                return json(['code' => 400, 'msg' => '确认码错误，操作被拒绝']);
            }

            // 获取所有有余额的用户
            $users = Db::name('user')
                ->field(['id', 'username', 'platform_money'])
                ->where('platform_money', '>', 0)
                ->select()
                ->toArray();

            if (empty($users)) {
                return json(['code' => 200, 'msg' => '没有需要清除余额的用户']);
            }

            $totalCleared = 0;
            $clearedCount = 0;

            // 开启事务
            Db::startTrans();

            try {
                foreach ($users as $user) {
                    $currentBalance = floatval($user['platform_money']);

                    // 更新用户余额为0
                    Db::name('user')
                        ->where('username', $user['username'])
                        ->update(['platform_money' => 0]);

                    // 记录资金变动日志
                    Db::name('user_money_log')->insert([
                        'user_id' => $user['id'],
                        'change' => -$currentBalance,
                        'reason' => '管理员批量清除余额',
                        'source' => 'Platform',
                        'create_time' => time()
                    ]);

                    $totalCleared += $currentBalance;
                    $clearedCount++;
                }

                // 提交事务
                Db::commit();

                // 记录操作日志
                \think\facade\Log::info("管理员批量清除所有用户余额：清除用户数={$clearedCount}, 总清除金额={$totalCleared}");

                return json([
                    'code' => 200,
                    'msg' => '批量清除余额成功',
                    'data' => [
                        'cleared_count' => $clearedCount,
                        'total_cleared_amount' => number_format($totalCleared, 2)
                    ]
                ]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('批量清除余额失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '批量清除失败：' . $e->getMessage()]);
        }
    }

    // 批量清除选中用户余额
    public function batchClearBalance() {
        try {
            $usernames = input('usernames', []);
            $confirmCode = input('confirm_code', '');

            // 安全确认码验证
            if ($confirmCode !== 'BATCH_CLEAR_CONFIRM') {
                return json(['code' => 400, 'msg' => '确认码错误，操作被拒绝']);
            }

            if (empty($usernames) || !is_array($usernames)) {
                return json(['code' => 400, 'msg' => '请选择要清除余额的用户']);
            }

            // 获取选中的有余额的用户
            $users = Db::name('user')
                ->field(['id', 'username', 'platform_money'])
                ->where('username', 'in', $usernames)
                ->where('platform_money', '>', 0)
                ->select()
                ->toArray();

            if (empty($users)) {
                return json(['code' => 200, 'msg' => '选中的用户中没有需要清除余额的用户']);
            }

            $totalCleared = 0;
            $clearedCount = 0;
            $clearedUsers = [];

            // 开启事务
            Db::startTrans();

            try {
                foreach ($users as $user) {
                    $currentBalance = floatval($user['platform_money']);

                    // 更新用户余额为0
                    Db::name('user')
                        ->where('username', $user['username'])
                        ->update(['platform_money' => 0]);

                    // 记录资金变动日志
                    Db::name('user_money_log')->insert([
                        'user_id' => $user['id'],
                        'change' => -$currentBalance,
                        'reason' => '管理员批量清除余额',
                        'source' => 'Platform',
                        'create_time' => time()
                    ]);

                    $totalCleared += $currentBalance;
                    $clearedCount++;
                    $clearedUsers[] = $user['username'];
                }

                // 提交事务
                Db::commit();

                // 记录操作日志
                \think\facade\Log::info("管理员批量清除选中用户余额：清除用户=" . implode(',', $clearedUsers) . ", 清除用户数={$clearedCount}, 总清除金额={$totalCleared}");

                return json([
                    'code' => 200,
                    'msg' => '批量清除余额成功',
                    'data' => [
                        'cleared_count' => $clearedCount,
                        'total_cleared_amount' => number_format($totalCleared, 2),
                        'cleared_users' => $clearedUsers
                    ]
                ]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('批量清除选中用户余额失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '批量清除失败：' . $e->getMessage()]);
        }
    }

    // 获取用户资金明细
    public function getMoneyLog() {
        try {
            $username = input('username', '');
            $search = input('search', '');
            $page = input('page/d', 1);
            $limit = input('limit/d', 20);
            
            if (empty($username)) {
                return json(['code' => 400, 'msg' => '请提供用户名']);
            }

            // 先通过用户名查询用户ID
            $user = Db::name('user')
                ->where('username', $username)
                ->field(['id'])
                ->find();

            if (empty($user)) {
                return json(['code' => 404, 'msg' => '未找到该用户']);
            }

            $query = Db::name('user_money_log')
                ->where([
                    'user_id' => $user['id'],
                    'source' => 'Platform'
                ])
                ->field([
                    'id',
                    'user_id',
                    'reason',
                    'create_time',
                    'change',
                    'source'
                ]);

            // 添加搜索条件
            if (!empty($search)) {
                $query->where('reason', 'like', "%{$search}%");
            }

            // 获取总数
            $total = $query->count();

            // 执行分页查询
            $logs = $query->order('create_time', 'desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            if (empty($logs)) {
                return json(['code' => 404, 'msg' => '没有找到资金记录']);
            }

            // 格式化数据
            $formattedLogs = array_map(function($log) {
                return [
                    'reason' => $log['reason'],
                    'create_time' => date('Y-m-d H:i:s', $log['create_time']),
                    'change' => number_format(floatval($log['change']), 2),
                    'source' => $log['source']
                ];
            }, $logs);

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $formattedLogs,
                'total' => $total
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('资金明细查询失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }
} 